# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* bssic_requests
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-20 12:00+0000\n"
"PO-Revision-Date: 2024-12-20 12:00+0000\n"
"Last-Translator: BSIC Team\n"
"Language-Team: Arabic\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: bssic_requests
#: model:ir.module.category,name:bssic_requests.module_category_bssic_requests
msgid "BSIC Requests"
msgstr "طلبات BSIC"

#. module: bssic_requests
#: model_terms:ir.ui.menu,name:bssic_requests.menu_bssic_root
msgid "BSIC Requests"
msgstr "طلبات BSIC"

#. module: bssic_requests
#: model_terms:ir.ui.menu,name:bssic_requests.menu_bssic_request
msgid "Requests"
msgstr "الطلبات"

#. module: bssic_requests
#: model_terms:ir.ui.menu,name:bssic_requests.menu_bssic_stationery
msgid "Stationery"
msgstr "القرطاسية"

#. module: bssic_requests
#: model_terms:ir.ui.menu,name:bssic_requests.menu_bssic_configuration
msgid "Configuration"
msgstr "الإعدادات"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_password_reset
msgid "Password Reset"
msgstr "إعادة تعيين كلمة المرور"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_usb
msgid "USB Access"
msgstr "الوصول إلى USB"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_extension
msgid "Extension Request"
msgstr "طلب تمديد"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_permission
msgid "Permission Request"
msgstr "طلب صلاحية"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_email
msgid "Email Request"
msgstr "طلب بريد إلكتروني"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_technical
msgid "Technical Request"
msgstr "طلب تقني"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__name
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__name
msgid "Request Reference"
msgstr "مرجع الطلب"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__employee_id
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__employee_id
msgid "Employee"
msgstr "الموظف"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__state
msgid "Status"
msgstr "الحالة"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Draft"
msgstr "مسودة"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Submitted"
msgstr "مُقدم"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Completed"
msgstr "مكتمل"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Rejected"
msgstr "مرفوض"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Submit"
msgstr "إرسال"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Reject"
msgstr "رفض"

#. module: bssic_requests
#: code:addons/bssic_requests/models/base_request.py:14
msgid "New"
msgstr "جديد"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_authorization_delegation
msgid "Authorization Delegation"
msgstr "تفويض صلاحية في سقف المستخدم"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_free_entry
msgid "Free Form"
msgstr "القيد الحر"

#. module: bssic_requests
#: model:bssic.technical.category,name:bssic_requests.technical_category_hardware
msgid "Hardware"
msgstr "الأجهزة"

#. module: bssic_requests
#: model:bssic.technical.category,name:bssic_requests.technical_category_software
msgid "Software"
msgstr "البرمجيات"

#. module: bssic_requests
#: model:bssic.technical.category,name:bssic_requests.technical_category_network
msgid "Network"
msgstr "الشبكة"

#. module: bssic_requests
#: model:res.groups,name:bssic_requests.group_bssic_employee
msgid "Employee"
msgstr "موظف"

#. module: bssic_requests
#: model:res.groups,name:bssic_requests.group_bssic_direct_manager
msgid "Direct Manager"
msgstr "المدير المباشر"

#. module: bssic_requests
#: model:res.groups,name:bssic_requests.group_bssic_audit_manager
msgid "Audit Manager"
msgstr "مدير التدقيق"

#. module: bssic_requests
#: model:res.groups,name:bssic_requests.group_bssic_it_manager
msgid "IT Manager"
msgstr "مدير تقنية المعلومات"

#. module: bssic_requests
#: model:res.groups,name:bssic_requests.group_bssic_it_staff
msgid "IT Staff"
msgstr "موظف تقنية المعلومات"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__priority
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__priority
msgid "Low"
msgstr "منخفضة"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__priority
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__priority
msgid "Normal"
msgstr "عادية"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__priority
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__priority
msgid "High"
msgstr "عالية"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__priority
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__priority
msgid "Urgent"
msgstr "عاجلة"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__request_type_id
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__request_type_id
msgid "Request Type"
msgstr "نوع الطلب"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__request_date
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__request_date
msgid "Request Date"
msgstr "تاريخ الطلب"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__description
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__description
msgid "Description"
msgstr "الوصف"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__department_id
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__department_id
msgid "Department"
msgstr "القسم"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__job_id
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__job_id
msgid "Job Position"
msgstr "المنصب الوظيفي"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__assigned_to
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__assigned_to
msgid "Assigned To"
msgstr "مُعين إلى"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__rejection_reason
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__rejection_reason
msgid "Rejection Reason"
msgstr "سبب الرفض"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__completion_notes
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__completion_notes
msgid "Completion Notes"
msgstr "ملاحظات الإنجاز"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__username
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__username
msgid "Username"
msgstr "اسم المستخدم"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__priority
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__priority
msgid "Priority"
msgstr "الأولوية"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Approve (Direct Manager)"
msgstr "موافقة (المدير المباشر)"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Approve (Audit Manager)"
msgstr "موافقة (مدير التدقيق)"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Approve (IT Manager)"
msgstr "موافقة (مدير تقنية المعلومات)"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Assign to IT Staff"
msgstr "تعيين لموظف تقنية المعلومات"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Start Implementation"
msgstr "بدء التنفيذ"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Mark as Completed"
msgstr "تحديد كمكتمل"

#. module: bssic_requests
#: model_terms:ir.actions.act_window,name:bssic_requests.action_bssic_requests
msgid "Requests"
msgstr "الطلبات"

#. module: bssic_requests
#: model_terms:ir.actions.act_window,help:bssic_requests.action_bssic_requests
msgid "Create a new request"
msgstr "إنشاء طلب جديد"

#. module: bssic_requests
#: model_terms:ir.actions.act_window,help:bssic_requests.action_bssic_requests
msgid "Create different types of requests that will go through an approval workflow."
msgstr "إنشاء أنواع مختلفة من الطلبات التي ستمر عبر سير عمل الموافقة."

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_authorization_delegation
msgid "Authorization Delegation"
msgstr "تفويض صلاحية في سقف المستخدم"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_authorization_delegation_request_form
msgid "Authorization Delegation Request"
msgstr "طلب تفويض صلاحية في سقف المستخدم"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_authorization_delegation_request_form
msgid "Authorization Delegation Details"
msgstr "تفاصيل تفويض الصلاحية"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_authorization_delegation_request_tree
msgid "Authorization Delegation Requests"
msgstr "طلبات تفويض الصلاحية"

#. module: bssic_requests
#: model_terms:ir.actions.act_window,name:bssic_requests.action_authorization_delegation_request
msgid "Authorization Delegation Requests"
msgstr "طلبات تفويض الصلاحية"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__ceiling_reason
msgid "Reason for Ceiling Increase"
msgstr "سبب رفع السقف"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__delegation_details
msgid "Details"
msgstr "التفاصيل"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__delegation_from_date
msgid "From Date"
msgstr "التاريخ من"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__delegation_to_date
msgid "To Date"
msgstr "التاريخ إلى"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__delegation_max_amount
msgid "Max. Amount"
msgstr "الحد الأقصى للمبلغ"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__delegation_auth_limit
msgid "Auth O.D. Limit"
msgstr "حد التفويض"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__show_authorization_delegation_fields
msgid "Show Authorization Delegation Fields"
msgstr "إظهار حقول تفويض الصلاحية"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_authorization_delegation_request_form
msgid "Create your first Authorization Delegation Request!"
msgstr "أنشئ أول طلب تفويض صلاحية!"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_authorization_delegation_request_form
msgid "Submit requests for authorization delegation in user ceiling limits."
msgstr "قدم طلبات تفويض الصلاحية في حدود سقف المستخدم."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:685
msgid "Reason for Ceiling Increase is required for Authorization Delegation requests."
msgstr "سبب رفع السقف مطلوب لطلبات تفويض الصلاحية."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:689
msgid "Details are required for Authorization Delegation requests."
msgstr "التفاصيل مطلوبة لطلبات تفويض الصلاحية."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:693
msgid "From Date is required for Authorization Delegation requests."
msgstr "التاريخ من مطلوب لطلبات تفويض الصلاحية."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:697
msgid "To Date is required for Authorization Delegation requests."
msgstr "التاريخ إلى مطلوب لطلبات تفويض الصلاحية."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:701
msgid "Max. Amount must be greater than zero for Authorization Delegation requests."
msgstr "الحد الأقصى للمبلغ يجب أن يكون أكبر من الصفر لطلبات تفويض الصلاحية."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:705
msgid "Auth O.D. Limit must be greater than zero for Authorization Delegation requests."
msgstr "حد التفويض يجب أن يكون أكبر من الصفر لطلبات تفويض الصلاحية."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:710
msgid "To Date must be after From Date in Authorization Delegation requests."
msgstr "التاريخ إلى يجب أن يكون بعد التاريخ من في طلبات تفويض الصلاحية."

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_free_entry
msgid "Free Form"
msgstr "القيد الحر"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_free_entry_request_form
msgid "Free Form Request"
msgstr "طلب القيد الحر"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_free_entry_request_form
msgid "Free Form Details"
msgstr "تفاصيل القيد الحر"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_free_entry_request_tree
msgid "Free Form Requests"
msgstr "طلبات القيد الحر"

#. module: bssic_requests
#: model_terms:ir.actions.act_window,name:bssic_requests.action_free_entry_request
msgid "Free Form Requests"
msgstr "طلبات القيد الحر"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__free_entry_subject
msgid "Subject"
msgstr "الموضوع"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__free_entry_details
msgid "Operation Details"
msgstr "تفاصيل العملية"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__free_entry_from_date
msgid "From Date"
msgstr "التاريخ من"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__free_entry_to_date
msgid "To Date"
msgstr "التاريخ إلى"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__free_entry_user_name
msgid "User Name"
msgstr "اسم المستخدم"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__free_entry_type
msgid "Entry Type"
msgstr "نوع القيد"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__free_entry_type
msgid "Free Entry"
msgstr "القيد الحر"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__free_entry_type
msgid "Reverse Entry"
msgstr "عكس قيد"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__show_free_entry_fields
msgid "Show Free Entry Fields"
msgstr "إظهار حقول القيد الحر"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_free_entry_request_form
msgid "Create your first Free Form Request!"
msgstr "أنشئ أول طلب قيد حر!"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_free_entry_request_form
msgid "Submit requests for free form authorization."
msgstr "قدم طلبات تفويض القيد الحر."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:732
msgid "Subject is required for Free Form requests."
msgstr "الموضوع مطلوب لطلبات القيد الحر."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:736
msgid "Operation Details are required for Free Form requests."
msgstr "تفاصيل العملية مطلوبة لطلبات القيد الحر."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:740
msgid "From Date is required for Free Form requests."
msgstr "التاريخ من مطلوب لطلبات القيد الحر."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:744
msgid "To Date is required for Free Form requests."
msgstr "التاريخ إلى مطلوب لطلبات القيد الحر."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:753
msgid "User Name is required for Free Form requests."
msgstr "اسم المستخدم مطلوب لطلبات القيد الحر."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:757
msgid "Entry Type is required for Free Form requests."
msgstr "نوع القيد مطلوب لطلبات القيد الحر."

#. module: bssic_requests
#: code:addons/bssic_requests/models/request.py:762
msgid "To Date must be after From Date in Free Form requests."
msgstr "التاريخ إلى يجب أن يكون بعد التاريخ من في طلبات القيد الحر."
