# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* bssic_requests
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-20 12:00+0000\n"
"PO-Revision-Date: 2024-12-20 12:00+0000\n"
"Last-Translator: BSIC Team\n"
"Language-Team: English\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: bssic_requests
#: model:ir.module.category,name:bssic_requests.module_category_bssic_requests
msgid "BSIC Requests"
msgstr "BSIC Requests"

#. module: bssic_requests
#: model_terms:ir.ui.menu,name:bssic_requests.menu_bssic_root
msgid "BSIC Requests"
msgstr "BSIC Requests"

#. module: bssic_requests
#: model_terms:ir.ui.menu,name:bssic_requests.menu_bssic_request
msgid "Requests"
msgstr "Requests"

#. module: bssic_requests
#: model_terms:ir.ui.menu,name:bssic_requests.menu_bssic_stationery
msgid "Stationery"
msgstr "Stationery"

#. module: bssic_requests
#: model_terms:ir.ui.menu,name:bssic_requests.menu_bssic_configuration
msgid "Configuration"
msgstr "Configuration"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_password_reset
msgid "Password Reset"
msgstr "Password Reset"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_usb
msgid "USB Access"
msgstr "USB Access"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_extension
msgid "Extension Request"
msgstr "Extension Request"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_permission
msgid "Permission Request"
msgstr "Permission Request"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_email
msgid "Email Request"
msgstr "Email Request"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_technical
msgid "Technical Request"
msgstr "Technical Request"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_authorization_delegation
msgid "Authorization Delegation"
msgstr "Authorization Delegation"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_free_entry
msgid "Free Form"
msgstr "Free Form"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__name
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__name
msgid "Request Reference"
msgstr "Request Reference"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__employee_id
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__employee_id
msgid "Employee"
msgstr "Employee"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__state
msgid "Status"
msgstr "Status"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Draft"
msgstr "Draft"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Submitted"
msgstr "Submitted"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Completed"
msgstr "Completed"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Rejected"
msgstr "Rejected"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Submit"
msgstr "Submit"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Reject"
msgstr "Reject"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__priority
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__priority
msgid "Low"
msgstr "Low"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__priority
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__priority
msgid "Normal"
msgstr "Normal"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__priority
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__priority
msgid "High"
msgstr "High"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__priority
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__priority
msgid "Urgent"
msgstr "Urgent"

#. module: bssic_requests
#: code:addons/bssic_requests/models/base_request.py:14
msgid "New"
msgstr "New"
