# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.tools.translate import _


class TestBSSICRequestsTranslation(TransactionCase):
    """Test translation functionality for BSSIC Requests module"""

    def setUp(self):
        super(TestBSSICRequestsTranslation, self).setUp()
        
        # Create test employee
        self.employee = self.env['hr.employee'].create({
            'name': 'Test Employee',
            'int_id': '12345'
        })
        
        # Create test request type
        self.request_type = self.env['bssic.request.type'].create({
            'name': 'Test Request Type',
            'code': 'test',
            'show_password_fields': True
        })

    def test_request_type_translation(self):
        """Test that request types can be translated"""
        # Test Password Reset request type
        password_reset_type = self.env.ref('bssic_requests.request_type_password_reset', raise_if_not_found=False)
        if password_reset_type:
            self.assertEqual(password_reset_type.name, 'Password Reset')
        
        # Test USB Access request type
        usb_type = self.env.ref('bssic_requests.request_type_usb', raise_if_not_found=False)
        if usb_type:
            self.assertEqual(usb_type.name, 'USB Access')

    def test_request_states_translation(self):
        """Test that request states are translatable"""
        request = self.env['bssic.request'].create({
            'employee_id': self.employee.id,
            'request_type_id': self.request_type.id,
            'description': 'Test request'
        })
        
        # Test state values
        states = dict(request._fields['state'].selection)
        self.assertIn('draft', states)
        self.assertIn('submitted', states)
        self.assertIn('completed', states)
        self.assertIn('rejected', states)

    def test_menu_translation(self):
        """Test that menu items are translatable"""
        # Test main menu
        main_menu = self.env.ref('bssic_requests.menu_bssic_root', raise_if_not_found=False)
        if main_menu:
            self.assertEqual(main_menu.name, 'BSIC Requests')

    def test_field_labels_translation(self):
        """Test that field labels are translatable"""
        request = self.env['bssic.request'].create({
            'employee_id': self.employee.id,
            'request_type_id': self.request_type.id,
            'description': 'Test request'
        })
        
        # Test field descriptions
        fields_info = request.fields_get()
        
        # Check some key fields
        if 'employee_id' in fields_info:
            self.assertEqual(fields_info['employee_id']['string'], 'Employee')
        
        if 'state' in fields_info:
            self.assertEqual(fields_info['state']['string'], 'Status')
        
        if 'request_type_id' in fields_info:
            self.assertEqual(fields_info['request_type_id']['string'], 'Request Type')

    def test_priority_translation(self):
        """Test that priority levels are translatable"""
        request = self.env['bssic.request'].create({
            'employee_id': self.employee.id,
            'request_type_id': self.request_type.id,
            'description': 'Test request',
            'priority': '2'  # High priority
        })
        
        # Test priority selection values
        priority_selection = dict(request._fields['priority'].selection)
        self.assertIn('0', priority_selection)  # Low
        self.assertIn('1', priority_selection)  # Normal
        self.assertIn('2', priority_selection)  # High
        self.assertIn('3', priority_selection)  # Urgent

    def test_technical_category_translation(self):
        """Test that technical categories are translatable"""
        # Test Hardware category
        hardware_category = self.env.ref('bssic_requests.technical_category_hardware', raise_if_not_found=False)
        if hardware_category:
            self.assertEqual(hardware_category.name, 'Hardware')
        
        # Test Software category
        software_category = self.env.ref('bssic_requests.technical_category_software', raise_if_not_found=False)
        if software_category:
            self.assertEqual(software_category.name, 'Software')

    def test_security_groups_translation(self):
        """Test that security groups are translatable"""
        # Test Employee group
        employee_group = self.env.ref('bssic_requests.group_bssic_employee', raise_if_not_found=False)
        if employee_group:
            self.assertEqual(employee_group.name, 'Employee')
        
        # Test IT Manager group
        it_manager_group = self.env.ref('bssic_requests.group_bssic_it_manager', raise_if_not_found=False)
        if it_manager_group:
            self.assertEqual(it_manager_group.name, 'IT Manager')

    def test_validation_messages_translation(self):
        """Test that validation messages are translatable"""
        # This test would require setting up specific validation scenarios
        # For now, we just ensure the translation infrastructure is in place
        
        # Test that the _() function is available for translations
        test_message = _("Test message")
        self.assertIsInstance(test_message, str)

    def test_help_text_translation(self):
        """Test that help texts are translatable"""
        request = self.env['bssic.request'].create({
            'employee_id': self.employee.id,
            'request_type_id': self.request_type.id,
            'description': 'Test request'
        })
        
        # Test field help texts
        fields_info = request.fields_get()
        
        # Check if help texts are present and translatable
        if 'employee_number' in fields_info and 'help' in fields_info['employee_number']:
            help_text = fields_info['employee_number']['help']
            self.assertIsInstance(help_text, str)
            self.assertTrue(len(help_text) > 0)

    def test_button_labels_translation(self):
        """Test that button labels in views are translatable"""
        # This would typically be tested through UI tests
        # For now, we ensure the translation keys are properly set up
        
        # Test that common button labels are defined
        submit_label = _("Submit")
        reject_label = _("Reject")
        approve_label = _("Approve")
        
        self.assertIsInstance(submit_label, str)
        self.assertIsInstance(reject_label, str)
        self.assertIsInstance(approve_label, str)

    def test_model_descriptions_translation(self):
        """Test that model descriptions are translatable"""
        # Test main request model
        request_model = self.env['ir.model'].search([('model', '=', 'bssic.request')], limit=1)
        if request_model:
            self.assertEqual(request_model.name, 'BSSIC Request')
        
        # Test request type model
        request_type_model = self.env['ir.model'].search([('model', '=', 'bssic.request.type')], limit=1)
        if request_type_model:
            self.assertEqual(request_type_model.name, 'BSSIC Request Type')
