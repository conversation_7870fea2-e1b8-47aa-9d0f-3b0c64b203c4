# دليل الترجمة لمديول BSIC Requests
# Translation Guide for BSIC Requests Module

## نظرة عامة / Overview

تم إضافة دعم الترجمة الكامل لمديول `bssic_requests` ليدعم اللغة العربية والإنجليزية.
Full translation support has been added to the `bssic_requests` module to support Arabic and English languages.

## الملفات المضافة / Added Files

### ملفات الترجمة / Translation Files
- `i18n/ar.po` - ملف الترجمة العربية / Arabic translation file
- `i18n/en.po` - ملف الترجمة الإنجليزية / English translation file

## كيفية تفعيل الترجمة / How to Enable Translation

### 1. تثبيت اللغة العربية / Install Arabic Language

1. اذهب إلى الإعدادات → اللغات / Go to Settings → Languages
2. انقر على "تحميل لغة" / Click "Load a Language"
3. اختر "العربية" / Select "Arabic"
4. انقر على "تحميل" / Click "Load"

### 2. تفعيل الترجمة للمستخدم / Enable Translation for User

1. اذهب إلى الإعدادات → المستخدمون / Go to Settings → Users
2. اختر المستخدم المطلوب / Select the desired user
3. في تبويب "التفضيلات" / In "Preferences" tab
4. اختر "العربية" من قائمة اللغة / Select "Arabic" from Language dropdown
5. احفظ التغييرات / Save changes

### 3. تحديث الترجمات / Update Translations

إذا كنت تريد تحديث الترجمات:
If you want to update translations:

```bash
# من مجلد Odoo الرئيسي / From main Odoo directory
python odoo-bin -d your_database -u bssic_requests --stop-after-init
```

## العناصر المترجمة / Translated Elements

### القوائم / Menus
- ✅ BSIC Requests → طلبات BSIC
- ✅ Requests → الطلبات  
- ✅ Stationery → القرطاسية
- ✅ Configuration → الإعدادات
- ✅ My Requests → طلباتي
- ✅ All Requests → جميع الطلبات

### أنواع الطلبات / Request Types
- ✅ Password Reset → إعادة تعيين كلمة المرور
- ✅ USB Access → الوصول إلى USB
- ✅ Extension Request → طلب تمديد
- ✅ Permission Request → طلب صلاحية
- ✅ Email Request → طلب بريد إلكتروني
- ✅ Technical Request → طلب تقني
- ✅ Authorization Delegation → تفويض صلاحية في سقف المستخدم
- ✅ Free Form → القيد الحر

### حالات الطلب / Request States
- ✅ Draft → مسودة
- ✅ Submitted → مُقدم
- ✅ Completed → مكتمل
- ✅ Rejected → مرفوض

### الأزرار / Buttons
- ✅ Submit → إرسال
- ✅ Reject → رفض
- ✅ Approve (Direct Manager) → موافقة (المدير المباشر)
- ✅ Approve (Audit Manager) → موافقة (مدير التدقيق)
- ✅ Approve (IT Manager) → موافقة (مدير تقنية المعلومات)

### الحقول / Fields
- ✅ Employee → الموظف
- ✅ Department → القسم
- ✅ Job Position → المنصب الوظيفي
- ✅ Request Type → نوع الطلب
- ✅ Request Date → تاريخ الطلب
- ✅ Description → الوصف
- ✅ Status → الحالة
- ✅ Priority → الأولوية

### مستويات الأولوية / Priority Levels
- ✅ Low → منخفضة
- ✅ Normal → عادية
- ✅ High → عالية
- ✅ Urgent → عاجلة

### المجموعات الأمنية / Security Groups
- ✅ Employee → موظف
- ✅ Direct Manager → المدير المباشر
- ✅ Audit Manager → مدير التدقيق
- ✅ IT Manager → مدير تقنية المعلومات
- ✅ IT Staff → موظف تقنية المعلومات

## اختبار الترجمة / Testing Translation

### 1. تغيير اللغة / Change Language
1. انقر على اسم المستخدم في الزاوية العلوية اليمنى
2. اختر "التفضيلات" / "Preferences"
3. غيّر اللغة إلى "العربية" أو "English"
4. احفظ التغييرات

### 2. التحقق من الترجمة / Verify Translation
- تحقق من أن القوائم تظهر باللغة المختارة
- تحقق من أن أسماء الحقول مترجمة
- تحقق من أن الأزرار مترجمة
- تحقق من أن رسائل الخطأ مترجمة

## إضافة ترجمات جديدة / Adding New Translations

لإضافة ترجمات جديدة:
To add new translations:

1. عدّل ملف `i18n/ar.po` للعربية
2. عدّل ملف `i18n/en.po` للإنجليزية
3. أعد تشغيل الخادم أو حدّث المديول

## الدعم / Support

إذا واجهت مشاكل في الترجمة، تأكد من:
If you encounter translation issues, make sure:

1. تم تثبيت اللغة العربية بشكل صحيح
2. تم تحديد اللغة للمستخدم
3. تم إعادة تشغيل الخادم بعد تحديث الترجمات
4. ملفات الترجمة موجودة في مجلد `i18n/`

## ملاحظات / Notes

- الترجمة تعمل تلقائياً حسب لغة المستخدم
- يمكن للمستخدمين المختلفين استخدام لغات مختلفة
- الترجمة تشمل جميع النصوص المرئية للمستخدم
- رسائل الخطأ والتحقق مترجمة أيضاً
